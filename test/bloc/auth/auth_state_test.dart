import 'package:flutter_test/flutter_test.dart';
import 'package:water_metering/bloc/auth/auth_state.dart';
import 'package:water_metering/core/error/auth_failures.dart';

import '../../helpers/test_fixtures.dart';

/// Unit tests for AuthState classes
/// 
/// These tests verify the equality, props, and toString implementations
/// of all authentication state classes.
void main() {
  group('AuthState', () {
    group('AuthInitial', () {
      test('should support equality comparison', () {
        // Arrange
        final state1 = AuthInitial();
        final state2 = AuthInitial();

        // Assert
        expect(state1, equals(state2));
        expect(state1.hashCode, equals(state2.hashCode));
      });

      test('should have empty props', () {
        // Arrange
        final state = AuthInitial();

        // Assert
        expect(state.props, equals([]));
      });

      test('should extend AuthState', () {
        // Arrange & Act
        final state = AuthInitial();

        // Assert
        expect(state, isA<AuthState>());
      });
    });

    group('AuthLoading', () {
      test('should support equality comparison', () {
        // Arrange
        final state1 = AuthLoading();
        final state2 = AuthLoading();

        // Assert
        expect(state1, equals(state2));
        expect(state1.hashCode, equals(state2.hashCode));
      });

      test('should have empty props', () {
        // Arrange
        final state = AuthLoading();

        // Assert
        expect(state.props, equals([]));
      });

      test('should extend AuthState', () {
        // Arrange & Act
        final state = AuthLoading();

        // Assert
        expect(state, isA<AuthState>());
      });
    });

    group('AuthAuthenticated', () {
      test('should support equality comparison with same user', () {
        // Arrange
        final user = TestFixtures.createTestAuthUser();
        final state1 = AuthAuthenticated(user);
        final state2 = AuthAuthenticated(user);

        // Assert
        expect(state1, equals(state2));
        expect(state1.hashCode, equals(state2.hashCode));
      });

      test('should not be equal with different users', () {
        // Arrange
        final user1 = TestFixtures.createTestAuthUser();
        final user2 = TestFixtures.createTestAuthUser(
          id: 'different_id',
          email: '<EMAIL>',
        );
        final state1 = AuthAuthenticated(user1);
        final state2 = AuthAuthenticated(user2);

        // Assert
        expect(state1, isNot(equals(state2)));
        expect(state1.hashCode, isNot(equals(state2.hashCode)));
      });

      test('should have correct props', () {
        // Arrange
        final user = TestFixtures.createTestAuthUser();
        final state = AuthAuthenticated(user);

        // Assert
        expect(state.props, equals([user]));
      });

      test('should have correct toString', () {
        // Arrange
        final user = TestFixtures.createTestAuthUser();
        final state = AuthAuthenticated(user);

        // Assert
        expect(state.toString(), equals('AuthAuthenticated(user: ${user.email})'));
      });

      test('should store user correctly', () {
        // Arrange
        final user = TestFixtures.createTestAuthUser();

        // Act
        final state = AuthAuthenticated(user);

        // Assert
        expect(state.user, equals(user));
      });

      test('should extend AuthState', () {
        // Arrange
        final user = TestFixtures.createTestAuthUser();

        // Act
        final state = AuthAuthenticated(user);

        // Assert
        expect(state, isA<AuthState>());
      });
    });

    group('AuthUnauthenticated', () {
      test('should support equality comparison', () {
        // Arrange
        final state1 = AuthUnauthenticated();
        final state2 = AuthUnauthenticated();

        // Assert
        expect(state1, equals(state2));
        expect(state1.hashCode, equals(state2.hashCode));
      });

      test('should have empty props', () {
        // Arrange
        final state = AuthUnauthenticated();

        // Assert
        expect(state.props, equals([]));
      });

      test('should have correct toString', () {
        // Arrange
        final state = AuthUnauthenticated();

        // Assert
        expect(state.toString(), equals('AuthUnauthenticated()'));
      });

      test('should extend AuthState', () {
        // Arrange & Act
        final state = AuthUnauthenticated();

        // Assert
        expect(state, isA<AuthState>());
      });
    });

    group('AuthTwoFactorRequired', () {
      test('should support equality comparison with same refCode', () {
        // Arrange
        final state1 = AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode);
        final state2 = AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode);

        // Assert
        expect(state1, equals(state2));
        expect(state1.hashCode, equals(state2.hashCode));
      });

      test('should not be equal with different refCode', () {
        // Arrange
        final state1 = AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode);
        final state2 = AuthTwoFactorRequired('different_ref_code');

        // Assert
        expect(state1, isNot(equals(state2)));
        expect(state1.hashCode, isNot(equals(state2.hashCode)));
      });

      test('should have correct props', () {
        // Arrange
        final state = AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode);

        // Assert
        expect(state.props, equals([TestFixtures.testTwoFactorRefCode]));
      });

      test('should have correct toString', () {
        // Arrange
        final state = AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode);

        // Assert
        expect(state.toString(), equals('AuthTwoFactorRequired(refCode: ${TestFixtures.testTwoFactorRefCode})'));
      });

      test('should store refCode correctly', () {
        // Arrange & Act
        final state = AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode);

        // Assert
        expect(state.refCode, equals(TestFixtures.testTwoFactorRefCode));
      });

      test('should extend AuthState', () {
        // Arrange & Act
        final state = AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode);

        // Assert
        expect(state, isA<AuthState>());
      });
    });

    group('AuthError', () {
      test('should support equality comparison with same failure', () {
        // Arrange
        final failure = TestFixtures.createInvalidCredentialsFailure();
        final state1 = AuthError(failure);
        final state2 = AuthError(failure);

        // Assert
        expect(state1, equals(state2));
        expect(state1.hashCode, equals(state2.hashCode));
      });

      test('should not be equal with different failures', () {
        // Arrange
        final failure1 = TestFixtures.createInvalidCredentialsFailure();
        final failure2 = TestFixtures.createNetworkFailure();
        final state1 = AuthError(failure1);
        final state2 = AuthError(failure2);

        // Assert
        expect(state1, isNot(equals(state2)));
        expect(state1.hashCode, isNot(equals(state2.hashCode)));
      });

      test('should have correct props', () {
        // Arrange
        final failure = TestFixtures.createInvalidCredentialsFailure();
        final state = AuthError(failure);

        // Assert
        expect(state.props, equals([failure]));
      });

      test('should have correct toString', () {
        // Arrange
        final failure = TestFixtures.createInvalidCredentialsFailure();
        final state = AuthError(failure);

        // Assert
        expect(state.toString(), equals('AuthError(failure: $failure)'));
      });

      test('should store failure correctly', () {
        // Arrange
        final failure = TestFixtures.createInvalidCredentialsFailure();

        // Act
        final state = AuthError(failure);

        // Assert
        expect(state.failure, equals(failure));
      });

      test('should extend AuthState', () {
        // Arrange
        final failure = TestFixtures.createInvalidCredentialsFailure();

        // Act
        final state = AuthError(failure);

        // Assert
        expect(state, isA<AuthState>());
      });

      test('should handle different failure types', () {
        // Arrange
        final networkFailure = TestFixtures.createNetworkFailure();
        final validationFailure = TestFixtures.createValidationFailure();
        final biometricFailure = TestFixtures.createBiometricFailure();

        // Act
        final networkState = AuthError(networkFailure);
        final validationState = AuthError(validationFailure);
        final biometricState = AuthError(biometricFailure);

        // Assert
        expect(networkState.failure, isA<NetworkFailure>());
        expect(validationState.failure, isA<ValidationFailure>());
        expect(biometricState.failure, isA<BiometricNotAvailableFailure>());
      });
    });

    group('inheritance', () {
      test('all states should extend AuthState', () {
        // Arrange
        final user = TestFixtures.createTestAuthUser();
        final failure = TestFixtures.createInvalidCredentialsFailure();

        // Act
        final initialState = AuthInitial();
        final loadingState = AuthLoading();
        final authenticatedState = AuthAuthenticated(user);
        final unauthenticatedState = AuthUnauthenticated();
        final twoFactorState = AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode);
        final errorState = AuthError(failure);

        // Assert
        expect(initialState, isA<AuthState>());
        expect(loadingState, isA<AuthState>());
        expect(authenticatedState, isA<AuthState>());
        expect(unauthenticatedState, isA<AuthState>());
        expect(twoFactorState, isA<AuthState>());
        expect(errorState, isA<AuthState>());
      });
    });

    group('edge cases', () {
      test('should handle empty refCode in AuthTwoFactorRequired', () {
        // Arrange & Act
        final state = AuthTwoFactorRequired('');

        // Assert
        expect(state.refCode, equals(''));
        expect(state.props, equals(['']));
      });

      test('should handle special characters in refCode', () {
        // Arrange
        const specialRefCode = 'ref-code-with-special-chars-123!@#';

        // Act
        final state = AuthTwoFactorRequired(specialRefCode);

        // Assert
        expect(state.refCode, equals(specialRefCode));
        expect(state.props, equals([specialRefCode]));
      });

      test('should handle different user properties in AuthAuthenticated', () {
        // Arrange
        final userWithTwoFactor = TestFixtures.createTestAuthUser(
          isTwoFactorEnabled: true,
        );
        final userWithoutEmailVerification = TestFixtures.createTestAuthUser(
          isEmailVerified: false,
        );

        // Act
        final state1 = AuthAuthenticated(userWithTwoFactor);
        final state2 = AuthAuthenticated(userWithoutEmailVerification);

        // Assert
        expect(state1.user.isTwoFactorEnabled, isTrue);
        expect(state2.user.isEmailVerified, isFalse);
        expect(state1, isNot(equals(state2)));
      });

      test('should handle various failure types in AuthError', () {
        // Arrange
        const twoFactorFailure = InvalidTwoFactorCodeFailure();
        const expiredFailure = TwoFactorExpiredFailure();

        // Act
        final twoFactorState = AuthError(twoFactorFailure);
        final expiredState = AuthError(expiredFailure);

        // Assert
        expect(twoFactorState.failure, isA<InvalidTwoFactorCodeFailure>());
        expect(expiredState.failure, isA<TwoFactorExpiredFailure>());
        expect(twoFactorState, isNot(equals(expiredState)));
      });
    });

    group('state transitions', () {
      test('should be able to create different state instances', () {
        // Arrange
        final user = TestFixtures.createTestAuthUser();
        final failure = TestFixtures.createInvalidCredentialsFailure();

        // Act
        final states = [
          AuthInitial(),
          AuthLoading(),
          AuthAuthenticated(user),
          AuthUnauthenticated(),
          AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode),
          AuthError(failure),
        ];

        // Assert
        expect(states.length, equals(6));
        for (int i = 0; i < states.length; i++) {
          for (int j = i + 1; j < states.length; j++) {
            expect(states[i], isNot(equals(states[j])));
          }
        }
      });
    });
  });
}
