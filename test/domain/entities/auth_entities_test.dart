import 'package:flutter_test/flutter_test.dart';
import 'package:water_metering/core/error/auth_failures.dart';
import 'package:water_metering/domain/entities/auth_result.dart';
import 'package:water_metering/domain/entities/auth_session.dart';
import 'package:water_metering/domain/entities/auth_user.dart';

import '../../helpers/test_fixtures.dart';

/// Unit tests for authentication domain entities
/// 
/// These tests verify the immutability, equality, and business logic
/// of domain entities following clean architecture principles.
void main() {
  group('AuthUser', () {
    test('should create instance with all required properties', () {
      // Act
      final user = TestFixtures.createTestAuthUser();

      // Assert
      expect(user.id, equals(TestFixtures.testUserId));
      expect(user.email, equals(TestFixtures.testEmail));
      expect(user.name, equals(TestFixtures.testUserName));
      expect(user.isEmailVerified, isTrue);
      expect(user.isTwoFactorEnabled, isFalse);
      expect(user.lastLoginAt, isNotNull);
      expect(user.createdAt, isNotNull);
    });

    test('should support equality comparison', () {
      // Arrange
      final user1 = TestFixtures.createTestAuthUser();
      final user2 = TestFixtures.createTestAuthUser();

      // Assert
      expect(user1, equals(user2));
      expect(user1.hashCode, equals(user2.hashCode));
    });

    test('should be immutable', () {
      // Arrange
      final user = TestFixtures.createTestAuthUser();
      final originalId = user.id;
      final originalEmail = user.email;

      // Act - Try to modify (should not be possible with immutable class)
      // This test verifies the class is properly designed as immutable

      // Assert
      expect(user.id, equals(originalId));
      expect(user.email, equals(originalEmail));
    });

    test('should handle different user configurations', () {
      // Arrange & Act
      final userWithTwoFactor = AuthUser(
        id: TestFixtures.testUserId,
        email: TestFixtures.testEmail,
        name: TestFixtures.testUserName,
        isEmailVerified: true,
        isTwoFactorEnabled: true,
        lastLoginAt: DateTime.now(),
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      );

      final userWithoutEmailVerification = AuthUser(
        id: TestFixtures.testUserId,
        email: TestFixtures.testEmail,
        name: TestFixtures.testUserName,
        isEmailVerified: false,
        isTwoFactorEnabled: false,
        lastLoginAt: DateTime.now(),
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      );

      // Assert
      expect(userWithTwoFactor.isTwoFactorEnabled, isTrue);
      expect(userWithoutEmailVerification.isEmailVerified, isFalse);
    });

    test('should handle edge cases for dates', () {
      // Arrange
      final now = DateTime.now();
      final user = AuthUser(
        id: TestFixtures.testUserId,
        email: TestFixtures.testEmail,
        name: TestFixtures.testUserName,
        isEmailVerified: true,
        isTwoFactorEnabled: false,
        lastLoginAt: now,
        createdAt: now, // Same time for creation and last login
      );

      // Assert
      expect(user.lastLoginAt, equals(user.createdAt));
    });
  });

  group('AuthSession', () {
    test('should create instance with all required properties', () {
      // Act
      final session = TestFixtures.createTestAuthSession();

      // Assert
      expect(session.id, equals(TestFixtures.testSessionId));
      expect(session.userId, equals(TestFixtures.testUserId));
      expect(session.accessToken, equals(TestFixtures.testAccessToken));
      expect(session.refreshToken, equals(TestFixtures.testRefreshToken));
      expect(session.expiresAt, isNotNull);
      expect(session.createdAt, isNotNull);
      expect(session.isActive, isTrue);
    });

    test('should support equality comparison', () {
      // Arrange
      final session1 = TestFixtures.createTestAuthSession();
      final session2 = TestFixtures.createTestAuthSession();

      // Assert
      expect(session1, equals(session2));
      expect(session1.hashCode, equals(session2.hashCode));
    });

    test('should be immutable', () {
      // Arrange
      final session = TestFixtures.createTestAuthSession();
      final originalId = session.id;
      final originalToken = session.accessToken;

      // Act - Verify immutability
      // This test verifies the class is properly designed as immutable

      // Assert
      expect(session.id, equals(originalId));
      expect(session.accessToken, equals(originalToken));
    });

    test('should handle expired sessions', () {
      // Arrange & Act
      final expiredSession = AuthSession(
        id: TestFixtures.testSessionId,
        userId: TestFixtures.testUserId,
        accessToken: TestFixtures.testAccessToken,
        refreshToken: TestFixtures.testRefreshToken,
        expiresAt: DateTime.now().subtract(const Duration(hours: 1)), // Expired
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        isActive: false,
      );

      // Assert
      expect(expiredSession.isActive, isFalse);
      expect(expiredSession.expiresAt.isBefore(DateTime.now()), isTrue);
    });

    test('should handle future expiration times', () {
      // Arrange & Act
      final futureSession = AuthSession(
        id: TestFixtures.testSessionId,
        userId: TestFixtures.testUserId,
        accessToken: TestFixtures.testAccessToken,
        refreshToken: TestFixtures.testRefreshToken,
        expiresAt: DateTime.now().add(const Duration(hours: 1)), // Future
        createdAt: DateTime.now(),
        isActive: true,
      );

      // Assert
      expect(futureSession.isActive, isTrue);
      expect(futureSession.expiresAt.isAfter(DateTime.now()), isTrue);
    });
  });

  group('AuthResult', () {
    group('success scenarios', () {
      test('should create successful result with user and tokens', () {
        // Act
        final result = TestFixtures.createSuccessAuthResult();

        // Assert
        expect(result.success, isTrue);
        expect(result.user, isNotNull);
        expect(result.accessToken, isNotNull);
        expect(result.refreshToken, isNotNull);
        expect(result.session, isNotNull);
        expect(result.failure, isNull);
        expect(result.requiresTwoFactor, isFalse);
        expect(result.twoFactorRefCode, isNull);
      });

      test('should support equality comparison for success results', () {
        // Arrange
        final result1 = TestFixtures.createSuccessAuthResult();
        final result2 = TestFixtures.createSuccessAuthResult();

        // Assert
        expect(result1, equals(result2));
        expect(result1.hashCode, equals(result2.hashCode));
      });
    });

    group('two-factor scenarios', () {
      test('should create two-factor required result', () {
        // Act
        final result = TestFixtures.createTwoFactorRequiredAuthResult();

        // Assert
        expect(result.success, isFalse);
        expect(result.requiresTwoFactor, isTrue);
        expect(result.twoFactorRefCode, isNotNull);
        expect(result.user, isNull);
        expect(result.accessToken, isNull);
        expect(result.refreshToken, isNull);
        expect(result.session, isNull);
        expect(result.failure, isNull);
      });

      test('should support equality comparison for two-factor results', () {
        // Arrange
        final result1 = TestFixtures.createTwoFactorRequiredAuthResult();
        final result2 = TestFixtures.createTwoFactorRequiredAuthResult();

        // Assert
        expect(result1, equals(result2));
        expect(result1.hashCode, equals(result2.hashCode));
      });
    });

    group('failure scenarios', () {
      test('should create failure result with error details', () {
        // Arrange
        final failure = TestFixtures.createInvalidCredentialsFailure();

        // Act
        final result = TestFixtures.createFailureAuthResult(failure: failure);

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, equals(failure));
        expect(result.user, isNull);
        expect(result.accessToken, isNull);
        expect(result.refreshToken, isNull);
        expect(result.session, isNull);
        expect(result.requiresTwoFactor, isFalse);
        expect(result.twoFactorRefCode, isNull);
      });

      test('should handle different failure types', () {
        // Arrange
        final networkFailure = TestFixtures.createNetworkFailure();
        final validationFailure = TestFixtures.createValidationFailure();
        final biometricFailure = TestFixtures.createBiometricFailure();

        // Act
        final networkResult = TestFixtures.createFailureAuthResult(failure: networkFailure);
        final validationResult = TestFixtures.createFailureAuthResult(failure: validationFailure);
        final biometricResult = TestFixtures.createFailureAuthResult(failure: biometricFailure);

        // Assert
        expect(networkResult.failure, isA<NetworkFailure>());
        expect(validationResult.failure, isA<ValidationFailure>());
        expect(biometricResult.failure, isA<BiometricNotAvailableFailure>());
      });

      test('should support equality comparison for failure results', () {
        // Arrange
        final failure = TestFixtures.createInvalidCredentialsFailure();
        final result1 = TestFixtures.createFailureAuthResult(failure: failure);
        final result2 = TestFixtures.createFailureAuthResult(failure: failure);

        // Assert
        expect(result1, equals(result2));
        expect(result1.hashCode, equals(result2.hashCode));
      });
    });

    group('immutability', () {
      test('should be immutable for success results', () {
        // Arrange
        final result = TestFixtures.createSuccessAuthResult();
        final originalSuccess = result.success;
        final originalUser = result.user;

        // Act - Verify immutability
        // This test verifies the class is properly designed as immutable

        // Assert
        expect(result.success, equals(originalSuccess));
        expect(result.user, equals(originalUser));
      });

      test('should be immutable for failure results', () {
        // Arrange
        final failure = TestFixtures.createInvalidCredentialsFailure();
        final result = TestFixtures.createFailureAuthResult(failure: failure);
        final originalSuccess = result.success;
        final originalFailure = result.failure;

        // Act - Verify immutability
        // This test verifies the class is properly designed as immutable

        // Assert
        expect(result.success, equals(originalSuccess));
        expect(result.failure, equals(originalFailure));
      });
    });

    group('edge cases', () {
      test('should handle null values appropriately', () {
        // Act
        final result = AuthResult(
          success: false,
          user: null,
          accessToken: null,
          refreshToken: null,
          session: null,
          failure: null,
          requiresTwoFactor: false,
          twoFactorRefCode: null,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.user, isNull);
        expect(result.accessToken, isNull);
        expect(result.refreshToken, isNull);
        expect(result.session, isNull);
        expect(result.failure, isNull);
        expect(result.requiresTwoFactor, isFalse);
        expect(result.twoFactorRefCode, isNull);
      });

      test('should handle mixed success and two-factor flags', () {
        // Act
        final result = AuthResult(
          success: false,
          user: null,
          accessToken: null,
          refreshToken: null,
          session: null,
          failure: null,
          requiresTwoFactor: true,
          twoFactorRefCode: TestFixtures.testTwoFactorRefCode,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.requiresTwoFactor, isTrue);
        expect(result.twoFactorRefCode, isNotNull);
      });
    });
  });
}
