import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:water_metering/core/error/auth_failures.dart';
import 'package:water_metering/domain/entities/auth_result.dart';
import 'package:water_metering/domain/entities/auth_user.dart';
import 'package:water_metering/domain/repositories/auth_repository.dart';

import '../../helpers/test_fixtures.dart';
import '../../helpers/test_helper.dart';

/// Unit tests for AuthRepository abstract interface
/// 
/// These tests verify that any implementation of AuthRepository
/// follows the expected contract and behavior patterns.
/// 
/// Since AuthRepository is abstract, we test it through a mock
/// implementation to verify the interface contract.
void main() {
  group('AuthRepository', () {
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
    });

    group('login', () {
      test('should return successful AuthResult when login succeeds', () async {
        // Arrange
        final expectedResult = TestFixtures.createSuccessAuthResult();
        when(() => mockAuthRepository.login(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await mockAuthRepository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isTrue);
        expect(result.user, isNotNull);
        expect(result.accessToken, isNotNull);
        expect(result.refreshToken, isNotNull);
        verify(() => mockAuthRepository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        )).called(1);
      });

      test('should return two-factor required result when 2FA is needed', () async {
        // Arrange
        final expectedResult = TestFixtures.createTwoFactorRequiredAuthResult();
        when(() => mockAuthRepository.login(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await mockAuthRepository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isFalse);
        expect(result.requiresTwoFactor, isTrue);
        expect(result.twoFactorRefCode, isNotNull);
        verify(() => mockAuthRepository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        )).called(1);
      });

      test('should return failure result when login fails', () async {
        // Arrange
        final expectedResult = TestFixtures.createFailureAuthResult(
          failure: TestFixtures.createInvalidCredentialsFailure(),
        );
        when(() => mockAuthRepository.login(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await mockAuthRepository.login(
          TestFixtures.testEmail,
          'wrong_password',
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isFalse);
        expect(result.failure, isA<InvalidCredentialsFailure>());
        verify(() => mockAuthRepository.login(
          TestFixtures.testEmail,
          'wrong_password',
        )).called(1);
      });

      test('should handle network failures', () async {
        // Arrange
        final expectedResult = TestFixtures.createFailureAuthResult(
          failure: TestFixtures.createNetworkFailure(),
        );
        when(() => mockAuthRepository.login(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await mockAuthRepository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isFalse);
        expect(result.failure, isA<NetworkFailure>());
      });
    });

    group('loginWithBiometric', () {
      test('should return successful AuthResult when biometric login succeeds', () async {
        // Arrange
        final expectedResult = TestFixtures.createSuccessAuthResult();
        when(() => mockAuthRepository.loginWithBiometric(any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await mockAuthRepository.loginWithBiometric(
          TestFixtures.testEmail,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isTrue);
        expect(result.user, isNotNull);
        verify(() => mockAuthRepository.loginWithBiometric(
          TestFixtures.testEmail,
        )).called(1);
      });

      test('should return failure when biometric authentication fails', () async {
        // Arrange
        final expectedResult = TestFixtures.createFailureAuthResult(
          failure: TestFixtures.createBiometricFailure(),
        );
        when(() => mockAuthRepository.loginWithBiometric(any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await mockAuthRepository.loginWithBiometric(
          TestFixtures.testEmail,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isFalse);
        expect(result.failure, isA<BiometricNotAvailableFailure>());
      });
    });

    group('verifyTwoFactor', () {
      test('should return successful AuthResult when 2FA verification succeeds', () async {
        // Arrange
        final expectedResult = TestFixtures.createSuccessAuthResult();
        when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await mockAuthRepository.verifyTwoFactor(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isTrue);
        expect(result.user, isNotNull);
        verify(() => mockAuthRepository.verifyTwoFactor(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        )).called(1);
      });

      test('should return failure when 2FA code is invalid', () async {
        // Arrange
        final expectedResult = TestFixtures.createFailureAuthResult(
          failure: const InvalidTwoFactorCodeFailure(),
        );
        when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await mockAuthRepository.verifyTwoFactor(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.invalidTwoFactorCode,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isFalse);
        expect(result.failure, isA<InvalidTwoFactorCodeFailure>());
      });
    });

    group('logout', () {
      test('should complete successfully', () async {
        // Arrange
        when(() => mockAuthRepository.logout())
            .thenAnswer((_) async => {});

        // Act & Assert
        expect(() => mockAuthRepository.logout(), returnsNormally);
        verify(() => mockAuthRepository.logout()).called(1);
      });
    });

    group('globalLogout', () {
      test('should complete successfully', () async {
        // Arrange
        when(() => mockAuthRepository.globalLogout())
            .thenAnswer((_) async => {});

        // Act & Assert
        expect(() => mockAuthRepository.globalLogout(), returnsNormally);
        verify(() => mockAuthRepository.globalLogout()).called(1);
      });
    });

    group('refreshToken', () {
      test('should return new access token when refresh succeeds', () async {
        // Arrange
        const newAccessToken = 'new_access_token_123';
        when(() => mockAuthRepository.refreshToken())
            .thenAnswer((_) async => newAccessToken);

        // Act
        final result = await mockAuthRepository.refreshToken();

        // Assert
        expect(result, equals(newAccessToken));
        verify(() => mockAuthRepository.refreshToken()).called(1);
      });

      test('should throw exception when refresh fails', () async {
        // Arrange
        when(() => mockAuthRepository.refreshToken())
            .thenThrow(Exception('Token refresh failed'));

        // Act & Assert
        expect(
          () => mockAuthRepository.refreshToken(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('isAuthenticated', () {
      test('should return true when user is authenticated', () async {
        // Arrange
        when(() => mockAuthRepository.isAuthenticated())
            .thenAnswer((_) async => true);

        // Act
        final result = await mockAuthRepository.isAuthenticated();

        // Assert
        expect(result, isTrue);
        verify(() => mockAuthRepository.isAuthenticated()).called(1);
      });

      test('should return false when user is not authenticated', () async {
        // Arrange
        when(() => mockAuthRepository.isAuthenticated())
            .thenAnswer((_) async => false);

        // Act
        final result = await mockAuthRepository.isAuthenticated();

        // Assert
        expect(result, isFalse);
        verify(() => mockAuthRepository.isAuthenticated()).called(1);
      });
    });

    group('getCurrentUser', () {
      test('should return user when authenticated', () async {
        // Arrange
        final expectedUser = TestFixtures.createTestAuthUser();
        when(() => mockAuthRepository.getCurrentUser())
            .thenAnswer((_) async => expectedUser);

        // Act
        final result = await mockAuthRepository.getCurrentUser();

        // Assert
        expect(result, equals(expectedUser));
        expect(result?.id, equals(TestFixtures.testUserId));
        expect(result?.email, equals(TestFixtures.testEmail));
        verify(() => mockAuthRepository.getCurrentUser()).called(1);
      });

      test('should return null when not authenticated', () async {
        // Arrange
        when(() => mockAuthRepository.getCurrentUser())
            .thenAnswer((_) async => null);

        // Act
        final result = await mockAuthRepository.getCurrentUser();

        // Assert
        expect(result, isNull);
        verify(() => mockAuthRepository.getCurrentUser()).called(1);
      });
    });
  });
}
