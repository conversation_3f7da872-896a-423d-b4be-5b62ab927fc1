import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:water_metering/core/error/auth_failures.dart';
import 'package:water_metering/domain/entities/auth_result.dart';
import 'package:water_metering/domain/usecases/login_usecase.dart';

import '../../helpers/test_fixtures.dart';
import '../../helpers/test_helper.dart';

/// Unit tests for LoginUseCase
/// 
/// These tests verify the business logic for standard email/password login
/// including input validation, email normalization, and proper delegation
/// to the repository layer.
void main() {
  group('LoginUseCase', () {
    late LoginUseCase loginUseCase;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      loginUseCase = LoginUseCase(mockAuthRepository);
    });

    group('call', () {
      test('should return successful result when login succeeds', () async {
        // Arrange
        final expectedResult = TestFixtures.createSuccessAuthResult();
        when(() => mockAuthRepository.login(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await loginUseCase.call(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isTrue);
        expect(result.user, isNotNull);
        verify(() => mockAuthRepository.login(
          TestFixtures.testEmail.toLowerCase(),
          TestFixtures.testPassword,
        )).called(1);
      });

      test('should normalize email to lowercase before calling repository', () async {
        // Arrange
        const upperCaseEmail = '<EMAIL>';
        final expectedResult = TestFixtures.createSuccessAuthResult();
        when(() => mockAuthRepository.login(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        await loginUseCase.call(upperCaseEmail, TestFixtures.testPassword);

        // Assert
        verify(() => mockAuthRepository.login(
          upperCaseEmail.toLowerCase(),
          TestFixtures.testPassword,
        )).called(1);
      });

      test('should trim whitespace from email and password', () async {
        // Arrange
        const emailWithSpaces = '  <EMAIL>  ';
        const passwordWithSpaces = '  password123  ';
        final expectedResult = TestFixtures.createSuccessAuthResult();
        when(() => mockAuthRepository.login(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        await loginUseCase.call(emailWithSpaces, passwordWithSpaces);

        // Assert
        verify(() => mockAuthRepository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        )).called(1);
      });

      test('should return two-factor required result when 2FA is needed', () async {
        // Arrange
        final expectedResult = TestFixtures.createTwoFactorRequiredAuthResult();
        when(() => mockAuthRepository.login(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await loginUseCase.call(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isFalse);
        expect(result.requiresTwoFactor, isTrue);
        expect(result.twoFactorRefCode, isNotNull);
      });

      test('should return failure result when repository returns failure', () async {
        // Arrange
        final expectedResult = TestFixtures.createFailureAuthResult(
          failure: TestFixtures.createInvalidCredentialsFailure(),
        );
        when(() => mockAuthRepository.login(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await loginUseCase.call(
          TestFixtures.testEmail,
          'wrong_password',
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isFalse);
        expect(result.failure, isA<InvalidCredentialsFailure>());
      });

      group('input validation', () {
        test('should return failure for invalid email format', () async {
          // Act
          final result = await loginUseCase.call(
            TestFixtures.invalidEmail,
            TestFixtures.testPassword,
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('email'));
          verifyNever(() => mockAuthRepository.login(any(), any()));
        });

        test('should return failure for empty email', () async {
          // Act
          final result = await loginUseCase.call(
            '',
            TestFixtures.testPassword,
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('email'));
          verifyNever(() => mockAuthRepository.login(any(), any()));
        });

        test('should return failure for empty password', () async {
          // Act
          final result = await loginUseCase.call(
            TestFixtures.testEmail,
            TestFixtures.emptyPassword,
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('password'));
          verifyNever(() => mockAuthRepository.login(any(), any()));
        });

        test('should return failure for whitespace-only password', () async {
          // Act
          final result = await loginUseCase.call(
            TestFixtures.testEmail,
            '   ',
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('password'));
          verifyNever(() => mockAuthRepository.login(any(), any()));
        });

        test('should return failure for whitespace-only email', () async {
          // Act
          final result = await loginUseCase.call(
            '   ',
            TestFixtures.testPassword,
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('email'));
          verifyNever(() => mockAuthRepository.login(any(), any()));
        });
      });

      group('edge cases', () {
        test('should handle repository exceptions gracefully', () async {
          // Arrange
          when(() => mockAuthRepository.login(any(), any()))
              .thenThrow(Exception('Network error'));

          // Act & Assert
          expect(
            () => loginUseCase.call(TestFixtures.testEmail, TestFixtures.testPassword),
            throwsA(isA<Exception>()),
          );
        });

        test('should handle very long email addresses', () async {
          // Arrange
          final longEmail = '${'a' * 100}@example.com';
          final expectedResult = TestFixtures.createSuccessAuthResult();
          when(() => mockAuthRepository.login(any(), any()))
              .thenAnswer((_) async => expectedResult);

          // Act
          final result = await loginUseCase.call(longEmail, TestFixtures.testPassword);

          // Assert
          expect(result.success, isTrue);
          verify(() => mockAuthRepository.login(
            longEmail.toLowerCase(),
            TestFixtures.testPassword,
          )).called(1);
        });

        test('should handle special characters in password', () async {
          // Arrange
          const specialPassword = 'P@ssw0rd!@#\$%^&*()';
          final expectedResult = TestFixtures.createSuccessAuthResult();
          when(() => mockAuthRepository.login(any(), any()))
              .thenAnswer((_) async => expectedResult);

          // Act
          final result = await loginUseCase.call(TestFixtures.testEmail, specialPassword);

          // Assert
          expect(result.success, isTrue);
          verify(() => mockAuthRepository.login(
            TestFixtures.testEmail,
            specialPassword,
          )).called(1);
        });
      });
    });
  });

  group('BiometricLoginUseCase', () {
    late BiometricLoginUseCase biometricLoginUseCase;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      biometricLoginUseCase = BiometricLoginUseCase(mockAuthRepository);
    });

    group('call', () {
      test('should return successful result when biometric login succeeds', () async {
        // Arrange
        final expectedResult = TestFixtures.createSuccessAuthResult();
        when(() => mockAuthRepository.loginWithBiometric(any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await biometricLoginUseCase.call(TestFixtures.testEmail);

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isTrue);
        expect(result.user, isNotNull);
        verify(() => mockAuthRepository.loginWithBiometric(
          TestFixtures.testEmail.toLowerCase(),
        )).called(1);
      });

      test('should normalize email to lowercase', () async {
        // Arrange
        const upperCaseEmail = '<EMAIL>';
        final expectedResult = TestFixtures.createSuccessAuthResult();
        when(() => mockAuthRepository.loginWithBiometric(any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        await biometricLoginUseCase.call(upperCaseEmail);

        // Assert
        verify(() => mockAuthRepository.loginWithBiometric(
          upperCaseEmail.toLowerCase(),
        )).called(1);
      });

      test('should return failure when biometric authentication fails', () async {
        // Arrange
        final expectedResult = TestFixtures.createFailureAuthResult(
          failure: TestFixtures.createBiometricFailure(),
        );
        when(() => mockAuthRepository.loginWithBiometric(any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await biometricLoginUseCase.call(TestFixtures.testEmail);

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isFalse);
        expect(result.failure, isA<BiometricNotAvailableFailure>());
      });

      group('input validation', () {
        test('should return failure for invalid email format', () async {
          // Act
          final result = await biometricLoginUseCase.call(TestFixtures.invalidEmail);

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('email'));
          verifyNever(() => mockAuthRepository.loginWithBiometric(any()));
        });

        test('should return failure for empty email', () async {
          // Act
          final result = await biometricLoginUseCase.call('');

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('email'));
          verifyNever(() => mockAuthRepository.loginWithBiometric(any()));
        });
      });
    });
  });
}
