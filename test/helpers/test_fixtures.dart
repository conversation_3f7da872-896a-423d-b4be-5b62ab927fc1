import 'package:water_metering/core/error/auth_failures.dart';
import 'package:water_metering/data/models/auth_session_model.dart';
import 'package:water_metering/data/models/auth_user_model.dart';
import 'package:water_metering/domain/entities/auth_result.dart';
import 'package:water_metering/domain/entities/auth_session.dart';
import 'package:water_metering/domain/entities/auth_user.dart';

/// Test fixtures providing sample data for authentication tests
///
/// This class contains static methods that return consistent test data
/// for use across different test files. This ensures test data consistency
/// and makes tests more maintainable.
class TestFixtures {
  // Test user data
  static const String testEmail = '<EMAIL>';
  static const String testPassword = 'password123';
  static const String testUserId = 'user123';
  static const String testUserName = 'Test User';
  static const String testAccessToken = 'access_token_123';
  static const String testRefreshToken = 'refresh_token_123';
  static const String testTwoFactorRefCode = '2fa_ref_123';
  static const String testTwoFactorCode = '123456';
  static const String testSessionId = 'session_123';
  static const String testPhone = '1234567890';

  // Invalid test data
  static const String invalidEmail = 'invalid-email';
  static const String emptyPassword = '';
  static const String invalidTwoFactorCode = '000000';

  /// Creates a test AuthUser domain entity
  static AuthUser createTestAuthUser({
    String? id,
    String? email,
    String? name,
    bool? emailVerified,
    String? phone,
    bool? phoneVerified,
    String? multiFactor,
  }) {
    return AuthUser(
      id: id ?? testUserId,
      email: email ?? testEmail,
      name: name ?? testUserName,
      emailVerified: emailVerified ?? true,
      phone: phone ?? '1234567890',
      phoneVerified: phoneVerified ?? true,
      multiFactor: multiFactor,
    );
  }

  /// Creates a test AuthUserModel data model
  static AuthUserModel createTestAuthUserModel({
    String? id,
    String? email,
    String? name,
    bool? emailVerified,
    String? phone,
    bool? phoneVerified,
    String? multiFactor,
  }) {
    return AuthUserModel(
      id: id ?? testUserId,
      email: email ?? testEmail,
      name: name ?? testUserName,
      emailVerified: emailVerified ?? true,
      phone: phone ?? '1234567890',
      phoneVerified: phoneVerified ?? true,
      multiFactor: multiFactor,
    );
  }

  /// Creates a test AuthSession domain entity
  static AuthSession createTestAuthSession({
    String? clientId,
    String? deviceInfo,
    String? location,
    DateTime? lastRefresh,
    String? accessToken,
    String? refreshToken,
    bool? isActive,
    DateTime? expiresAt,
  }) {
    return AuthSession(
      clientId: clientId ?? 'test_client_123',
      deviceInfo: deviceInfo ?? 'Test Device',
      location: location ?? 'Test Location',
      lastRefresh: lastRefresh ?? DateTime.now(),
      accessToken: accessToken ?? testAccessToken,
      refreshToken: refreshToken ?? testRefreshToken,
      isActive: isActive ?? true,
      expiresAt: expiresAt ?? DateTime.now().add(const Duration(hours: 1)),
    );
  }

  /// Creates a test AuthSessionModel data model
  static AuthSessionModel createTestAuthSessionModel({
    String? accessToken,
    String? refreshToken,
    int? createdAt,
    int? lastUpdated,
    String? email,
    bool? twoFactorEnabled,
    bool? biometricEnabled,
    bool isValid = true,
    bool isTokenExpiring = false,
  }) {
    final now = DateTime.now().millisecondsSinceEpoch;

    // Calculate timestamps based on desired validity state
    int sessionCreatedAt;
    int sessionLastUpdated;

    if (isValid && !isTokenExpiring) {
      // Recent session (valid and not expiring)
      sessionCreatedAt =
          createdAt ?? (now - (10 * 60 * 1000)); // 10 minutes ago
      sessionLastUpdated =
          lastUpdated ?? (now - (5 * 60 * 1000)); // 5 minutes ago
    } else if (isValid && isTokenExpiring) {
      // Session that's valid but token is expiring
      sessionCreatedAt = createdAt ?? (now - (60 * 60 * 1000)); // 1 hour ago
      sessionLastUpdated =
          lastUpdated ?? (now - (56 * 60 * 1000)); // 56 minutes ago (expiring)
    } else {
      // Invalid session (token expired)
      sessionCreatedAt =
          createdAt ?? (now - (2 * 60 * 60 * 1000)); // 2 hours ago
      sessionLastUpdated =
          lastUpdated ?? (now - (70 * 60 * 1000)); // 70 minutes ago (expired)
    }

    return AuthSessionModel(
      accessToken: accessToken ?? testAccessToken,
      refreshToken: refreshToken ?? testRefreshToken,
      createdAt: sessionCreatedAt,
      lastUpdated: sessionLastUpdated,
      email: email ?? testEmail,
      twoFactorEnabled: twoFactorEnabled ?? false,
      biometricEnabled: biometricEnabled ?? false,
    );
  }

  /// Creates a successful AuthResult
  static AuthResult createSuccessAuthResult({
    AuthUser? user,
    String? accessToken,
    String? refreshToken,
  }) {
    return AuthResult.success(
      user: user ?? createTestAuthUser(),
      accessToken: accessToken ?? testAccessToken,
      refreshToken: refreshToken ?? testRefreshToken,
    );
  }

  /// Creates a two-factor required AuthResult
  static AuthResult createTwoFactorRequiredAuthResult({
    String? twoFactorRefCode,
  }) {
    return AuthResult.twoFactorRequired(
      twoFactorRefCode: twoFactorRefCode ?? testTwoFactorRefCode,
    );
  }

  /// Creates a failed AuthResult with AuthFailure
  static AuthResult createFailureAuthResult({
    AuthFailure? failure,
  }) {
    return AuthResult.withFailure(
      failure: failure ?? const InvalidCredentialsFailure(),
    );
  }

  /// Creates a network failure
  static NetworkFailure createNetworkFailure({
    String? message,
  }) {
    return NetworkFailure(
      message: message ?? 'Network connection failed',
    );
  }

  /// Creates an invalid credentials failure
  static InvalidCredentialsFailure createInvalidCredentialsFailure({
    String? message,
  }) {
    return InvalidCredentialsFailure(
      message: message ?? 'Invalid email or password',
    );
  }

  /// Creates a server failure
  static ServerFailure createServerFailure({
    String? message,
  }) {
    return ServerFailure(
      message: message ?? 'Server error occurred',
    );
  }

  /// Creates a biometric failure
  static BiometricNotAvailableFailure createBiometricFailure({
    String? message,
  }) {
    return BiometricNotAvailableFailure(
      message: message ?? 'Biometric authentication not available',
    );
  }

  /// Creates test token map
  static Map<String, String> createTestTokens({
    String? accessToken,
    String? refreshToken,
  }) {
    return {
      'access_token': accessToken ?? testAccessToken,
      'refresh_token': refreshToken ?? testRefreshToken,
    };
  }
}
